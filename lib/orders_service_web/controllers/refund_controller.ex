defmodule OrdersServiceWeb.RefundController do
  @moduledoc false

  use OrdersServiceWeb, :controller
  use OpenApiSpex.ControllerSpecs
  use Params

  alias OpenApiSpex.Reference
  alias OpenApiSpex.Schema
  alias OrdersService.Auth
  alias OrdersService.Refunds
  alias OrdersService.Ticket
  alias OrdersServiceWeb.OpenAPI.RefundTicketRequest
  alias OrdersServiceWeb.OpenAPI.RefundTicketResponse
  alias OrdersServiceWeb.Plugs.Authorize

  require Logger

  tags ["Refunds"]

  plug Authorize,
       [
         rule: ["promoter", "refund", "write"],
         permission: ["order.edit"]
       ]
       when action in [:create]

  plug Authorize,
       [
         rule: ["promoter", "refund", "read"],
         permission: ["event.ticket.view"]
       ]
       when action in [:index]

  action_fallback OrdersServiceWeb.FallbackController

  operation :index,
    summary: "List refunded tickets",
    parameters: [
      eventId: [
        in: :query_parameter,
        description: "Event ID",
        type: :string,
        example: "fe05c7ce-9c57-4399-b8fb-b63398e06257",
        required: false
      ],
      sellerId: [
        in: :query_parameter,
        description: "Seller ID",
        type: :string,
        example: "fe05c7ce-9c57-4399-b8fb-b63398e06257",
        required: false
      ],
      orderId: [
        in: :query_parameter,
        description: "Order ID",
        type: :string,
        example: "fe05c7ce-9c57-4399-b8fb-b63398e06257",
        required: false
      ],
      page: [
        in: :query_parameter,
        description: "Page number",
        type: :integer,
        example: 0,
        required: false
      ],
      pageSize: [
        in: :query_parameter,
        description: "Page size",
        type: :integer,
        example: 0,
        required: false
      ]
    ],
    request_body: {},
    responses: %{
      :ok => {"Ticket", "application/json", RefundTicketResponse},
      :forbidden => %Reference{"$ref": "#/components/responses/forbidden"},
      :unauthorized => %Reference{"$ref": "#/components/responses/unauthorized"}
    }

  defparams(
    index_params(%{
      event_id: [field: Ecto.UUID],
      seller_id: [field: Ecto.UUID],
      order_id: [field: Ecto.UUID],
      page: [field: :integer],
      page_size: [field: :integer]
    })
  )

  def index(conn, params) do
    with {_, %{valid?: true} = changeset} <- {:params, index_params(params)},
         params = Params.to_map(changeset),
         {_, true} <- {:auth, access?(conn, params[:seller_id], params[:event_id])} do
      refunded_transactions = Refunds.list_refund_transactions(params)

      conn
      |> put_status(:ok)
      |> render(:index, refunds: refunded_transactions)
    else
      {:params, changeset} ->
        conn
        |> put_status(:bad_request)
        |> put_view(ChangesetJSON)
        |> render(:error, changeset: changeset)

      {:auth, false} ->
        conn
        |> put_status(:forbidden)
        |> json(%{error_code: :forbidden, message: "Forbidden access"})
    end
  end

  operation :create,
    summary: "Refund tickets",
    parameters: [
      ticketIds: [
        in: :body,
        schema: %Schema{type: :array, items: %Schema{type: :string, format: :uuid}},
        required: true
      ],
      reason: [
        in: :body,
        schema: %Schema{
          type: :string,
          enum: ["FAILURE_OR_GOODWILL_PROMOTER", "FAILURE_STAGEDATES", "TIMEDOUT"]
        },
        required: true
      ]
    ],
    request_body: {"application/json", RefundTicketRequest},
    responses: %{
      :ok => {"Ticket", "application/json", RefundTicketResponse},
      :bad_request => %Reference{"$ref": "#/components/responses/bad_request"},
      :forbidden => %Reference{"$ref": "#/components/responses/forbidden"},
      :internal_server_error => %Reference{"$ref": "#/components/responses/internal_server_error"},
      :not_found => %Reference{"$ref": "#/components/responses/not_found"},
      :unprocessable_entity => %Reference{"$ref": "#/components/responses/unprocessable_entity"},
      :unauthorized => %Reference{"$ref": "#/components/responses/unauthorized"}
    }

  defparams(
    create_params(%{
      ticket_ids!: [field: {:array, Ecto.UUID}],
      reason!: [field: Ecto.Enum, values: [:FAILURE_OR_GOODWILL_PROMOTER, :FAILURE_STAGEDATES, :TIMEDOUT]]
    })
  )

  def create(conn, %{"ticket_ids" => ticket_ids} = params) do
    Logger.info("User #{inspect(get_user_id(conn))} refunds tickets #{inspect(ticket_ids)}")

    with {_, %{valid?: true, changes: params} = _changeset} <-
           {:params, create_params(params)},
         {_, {:ok, tickets}} <- {:tickets, Ticket.get_tickets_by_id(ticket_ids)},
         {_, all_tickets_to_refund} <- {:expand_ticket_groups, expand_tickets_with_groups(tickets)},
         {_, :ok} <- {:validate_event_access, Auth.validate_event_access(conn, all_tickets_to_refund)},
         {_, :ok} <- {:validate_ticket_refundable, Refunds.validate_multiple_ticket_refundable(all_tickets_to_refund)},
         {_, {:ok, _refunded_tickets}} <-
           {:refund, Refunds.refund_tickets(all_tickets_to_refund, params, get_user_id(conn))} do
      all_ticket_ids = Enum.map(all_tickets_to_refund, & &1.id)
      refunded_transactions = Refunds.list_refund_transactions_by_ticket_ids(all_ticket_ids)

      conn
      |> put_status(:ok)
      |> render(:create, refunds: refunded_transactions)
    else
      {:params, changeset} ->
        conn
        |> put_status(:bad_request)
        |> put_view(ChangesetJSON)
        |> render(:error, changeset: changeset)

      {:tickets, {:error, errors}} ->
        conn
        |> put_status(:not_found)
        |> json(%{message: "One or more tickets were not found: #{inspect(errors)}", error_code: :ticket_not_found})

      {:validate_event_access, {:error, :unauthorized}} ->
        conn
        |> put_status(:forbidden)
        |> json(%{message: "Unauthorized event access", error_code: :forbidden})

      {:validate_ticket_refundable, {:error, error}} ->
        conn
        |> put_status(:unprocessable_entity)
        |> json(%{
          message: "One or more tickets are not refundable: #{inspect(error)}",
          error_code: :ticket_not_refundable
        })

      {:refund, {:error, error}} ->
        Logger.error("Refund processing failed: #{inspect(error)}")

        conn
        |> put_status(:internal_server_error)
        |> json(%{
          message: "Refund processing failed",
          error_code: :refund_processing_error
        })
    end
  end

  defp access?(conn, seller_id, event_id) do
    current_seller?(conn, seller_id) ||
      (not is_nil(event_id) && event_authorized?(conn, event_id, "event.ticket.view"))
  end

  @spec expand_tickets_with_groups(tickets :: [OrdersService.Ticket.t()]) :: [OrdersService.Ticket.t()]
  defp expand_tickets_with_groups(tickets) do
    {tickets_with_groups, individual_tickets} =
      Enum.split_with(tickets, fn ticket -> not is_nil(ticket.ticket_group_id) end)

    ticket_group_ids =
      tickets_with_groups
      |> Enum.map(& &1.ticket_group_id)
      |> Enum.uniq()

    group_tickets =
      case ticket_group_ids do
        [] ->
          []

        group_ids ->
          Ticket.get_all_by_group_ids(group_ids, [:ticket_history, :ticket_group, order_ticket: [:order]])
      end

    Enum.uniq_by(individual_tickets ++ group_tickets, & &1.id)
  end
end
