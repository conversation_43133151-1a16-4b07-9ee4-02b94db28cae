defmodule OrdersService.Refunds do
  @moduledoc """
  The Refunds context.
  """
  import Ecto.Query, warn: false

  alias Adyen.Services.Checkout
  alias Ecto.Multi
  alias OrdersService.Bill
  alias OrdersService.Order
  alias OrdersService.OrderHistory
  alias OrdersService.OrderTicket
  alias OrdersService.PersonalInformation
  alias OrdersService.Pubsub.Publisher.OrdersPublisher
  alias OrdersService.RefundPayload
  alias OrdersService.Refunds.RefundTransaction
  alias OrdersService.Repo
  alias OrdersService.Ticket, as: TicketDB
  alias OrdersService.TicketGroup
  alias OrdersService.TicketHistory
  alias OrdersService.Tickets.Ticket
  alias OrdersService.Tickets.TicketValidator
  alias OrdersService.TransactionHistory

  require Logger

  @currency "EUR"

  @doc """
  Simplified refund function that takes already validated tickets and processes refunds.
  This is called from the controller after all validations are done.
  Uses the new ticket group-aware refund logic.
  """
  @spec refund_tickets([TicketDB.t()], map(), user_id :: Ecto.UUID.t()) ::
          {:ok, [TicketDB.t()]} | {:error, any()}
  def refund_tickets(tickets, params, user_id) do
    params = Map.put(params, :user_id, user_id)

    with {_, :ok} <-
           {:refund_multiple_tickets, refund_multiple_tickets_with_groups(tickets, params)},
         {_, :ok} <-
           {:publish_order_updates, process_order_updates(tickets)} do
      {:ok, tickets}
    else
      {:refund_multiple_tickets, error} ->
        Logger.error("Can't refund tickets because of #{inspect(error)} some tickets could not be refunded")
        {:error, error}

      {:publish_order_updates, error} ->
        Logger.error("Can't publish order updates due to error: #{inspect(error)}")
        {:error, :order_update_publish_failed}
    end
  end

  @spec calculate_refunds_split(
          bill :: Bill.t(),
          ticket :: TicketDB.t() | TicketGroup.t(),
          balance_account_id :: String.t(),
          config :: map()
        ) :: [map()]
  def calculate_refunds_split(%Bill{} = bill, item, balance_account_id, config) do
    %Bill{
      presale_fee: presale_fee,
      presale_fee_tax: presale_fee_tax,
      system_fee: system_fee,
      system_fee_tax: system_fee_tax,
      promoter_kickback: kickback_sum,
      promoter_kickback_tax: promoter_kickback_tax,
      promoter_total: promoter_total,
      future_demand_fee: future_demand_fee,
      future_demand_fee_tax: future_demand_fee_tax,
      donation: donation,
      payment_method_fee: payment_method_fee,
      payment_method_fee_tax: payment_method_fee_tax
    } = bill

    reference = reference_from_item(item)

    %{
      "presaleFeeAdyenBalanceAccountId" => presale_fee_adyen_balance_account_id,
      "transactionFeeAdyenBalanceAccountId" => transaction_fee_adyen_balance_account_id,
      "defaultDonationBalanceAccountId" => default_donation_balance_account_id,
      "futureDemandFeeAdyenBalanceAccountId" => future_demand_fee_adyen_balance_account_id,
      "paymentMethodFeeAdyenBalanceAccountId" => payment_method_fee_adyen_balance_account_id
    } = config

    splits = []

    splits =
      if presale_fee == 0 do
        splits
      else
        [
          %{
            "type" => "Commission",
            "account" => presale_fee_adyen_balance_account_id,
            "reference" => "presale-#{reference}",
            "amount" => %{
              "currency" => @currency,
              "value" => presale_fee + presale_fee_tax
            }
          }
          | splits
        ]
      end

    splits =
      if system_fee == 0 do
        splits
      else
        [
          %{
            "type" => "Commission",
            "account" => transaction_fee_adyen_balance_account_id,
            "reference" => "transaction-#{reference}",
            "amount" => %{
              "currency" => @currency,
              "value" => system_fee + system_fee_tax
            }
          }
          | splits
        ]
      end

    splits =
      if kickback_sum == 0 do
        splits
      else
        [
          %{
            "type" => "Commission",
            "account" => balance_account_id,
            "reference" => "kickback-#{reference}",
            "amount" => %{
              "currency" => @currency,
              "value" => kickback_sum + promoter_kickback_tax
            }
          }
          | splits
        ]
      end

    splits =
      if promoter_total == 0 do
        splits
      else
        [
          %{
            "type" => "Commission",
            "account" => balance_account_id,
            "reference" => "promoter-#{reference}",
            "amount" => %{
              "currency" => @currency,
              "value" => promoter_total
            }
          }
          | splits
        ]
      end

    splits =
      if donation == 0 do
        splits
      else
        [
          %{
            "type" => "Commission",
            "account" => default_donation_balance_account_id,
            "reference" => "donation-#{reference}",
            "amount" => %{
              "currency" => @currency,
              "value" => donation
            }
          }
          | splits
        ]
      end

    splits =
      if future_demand_fee == 0 do
        splits
      else
        [
          %{
            "type" => "Commission",
            "account" => future_demand_fee_adyen_balance_account_id,
            "reference" => "future_demand-#{reference}",
            "amount" => %{
              "currency" => @currency,
              "value" => future_demand_fee + future_demand_fee_tax
            }
          }
          | splits
        ]
      end

    splits =
      if payment_method_fee == 0 do
        splits
      else
        [
          %{
            "type" => "Commission",
            "account" => payment_method_fee_adyen_balance_account_id,
            "reference" => "payment_method_fee-#{reference}",
            "amount" => %{
              "currency" => @currency,
              "value" => payment_method_fee + payment_method_fee_tax
            }
          }
          | splits
        ]
      end

    splits
  end

  @doc """
  Refund multiple tickets with proper ticket group handling.
  Groups tickets by ticket_group_id and creates one refund transaction per group.
  Individual tickets (not in groups) are refunded separately.
  Uses RefundPayload for unified handling.
  """
  @spec refund_multiple_tickets_with_groups(tickets :: [TicketDB.t()], params :: map()) ::
          :ok | {:error, any()}
  def refund_multiple_tickets_with_groups(tickets, %{reason: reason} = params) do
    Logger.debug("Refund multiple tickets with group handling for #{length(tickets)} tickets")

    with {:ok, payloads} <- build_unified_refund_payloads(tickets, reason) do
      refund_multiple_items(payloads, params)
    end
  end

  # Build unified refund payloads for tickets, automatically handling ticket groups.
  # Uses the enhanced RefundPayload.build function that can handle both tickets and ticket groups.
  @spec build_unified_refund_payloads(tickets :: [TicketDB.t()], reason :: atom()) ::
          {:ok, [RefundPayload.t()]} | {:error, any()}
  defp build_unified_refund_payloads(tickets, reason) do
    {grouped_tickets, individual_tickets} =
      Enum.split_with(tickets, fn ticket -> not is_nil(ticket.ticket_group_id) end)

    ticket_group_ids = grouped_tickets |> Enum.map(& &1.ticket_group_id) |> Enum.uniq()

    group_payloads_result =
      Enum.reduce_while(ticket_group_ids, {:ok, []}, fn ticket_group_id, {:ok, acc} ->
        case RefundPayload.build_for_ticket_group(ticket_group_id, reason) do
          {:ok, payload} ->
            {:cont, {:ok, [payload | acc]}}

          {:error, error} ->
            Logger.error("Failed to build refund payload for ticket group #{ticket_group_id}: #{inspect(error)}")
            {:halt, {:error, error}}
        end
      end)

    individual_payloads_result =
      Enum.reduce_while(individual_tickets, {:ok, []}, fn ticket, {:ok, acc} ->
        case RefundPayload.build_for_ticket(ticket.id, reason) do
          {:ok, payload} ->
            {:cont, {:ok, [payload | acc]}}

          {:error, error} ->
            Logger.error("Failed to build refund payload for ticket #{ticket.id}: #{inspect(error)}")
            {:halt, {:error, error}}
        end
      end)

    with {:ok, group_payloads} <- group_payloads_result,
         {:ok, individual_payloads} <- individual_payloads_result do
      {:ok, List.flatten([group_payloads | individual_payloads])}
    end
  end

  @spec refund_multiple_items(payloads :: [RefundPayload.t()], params :: map()) :: :ok | {:error, any()}
  defp refund_multiple_items(payloads, params) do
    Enum.reduce_while(payloads, :ok, fn payload, _acc ->
      case refund_item(payload, params) do
        :ok -> {:cont, :ok}
        {:error, error} -> {:halt, {:error, error}}
      end
    end)
  end

  @spec update_tickets_to_refunded(tickets :: [TicketDB.t()], user_id :: Ecto.UUID.t()) :: :ok | {:error, any()}
  defp update_tickets_to_refunded(tickets, user_id) do
    Enum.reduce_while(tickets, :ok, fn ticket, _acc ->
      case TicketDB.set_status(ticket, :REFUNDED, user_id, nil, nil, true, %{}) do
        {:ok, _updated_ticket} -> {:cont, :ok}
        {:error, error} -> {:halt, {:error, error}}
      end
    end)
  end

  @spec validate_multiple_ticket_refundable(tickets :: [TicketDB.t()]) :: :ok | {:error, list()}
  def validate_multiple_ticket_refundable(tickets) do
    Enum.reduce_while(tickets, :ok, fn ticket, _acc ->
      case TicketValidator.validate_ticket_refundable(ticket) do
        :ok -> {:cont, :ok}
        {:error, error} -> {:halt, {:error, error}}
      end
    end)
  end

  # Refund guest list items (no payment processing required).
  # Handles both individual tickets and ticket groups.
  defp refund_item(%RefundPayload{adyen_payload: nil, psp_reference: nil, item: item}, params) do
    %{user_id: user_id, reason: reason} = params

    case item do
      %TicketDB{} = ticket ->
        refund_direct_ticket(ticket, user_id, reason)

      %TicketGroup{} = ticket_group ->
        refund_direct_ticket_group(ticket_group, user_id, reason)
    end
  end

  # Refund paid items (require payment processing).
  # Handles both individual tickets and ticket groups.
  defp refund_item(%RefundPayload{adyen_payload: adyen_payload, psp_reference: psp_reference, item: item}, params)
       when not is_nil(adyen_payload) and not is_nil(psp_reference) do
    case item do
      %TicketDB{} = ticket ->
        refund_adyen_paid_ticket(ticket, adyen_payload, psp_reference, params)

      %TicketGroup{} = ticket_group ->
        refund_adyen_paid_ticket_group(ticket_group, adyen_payload, psp_reference, params)
    end
  end

  @spec refund_direct_ticket(TicketDB.t(), Ecto.UUID.t(), atom()) :: :ok | {:error, any()}
  defp refund_direct_ticket(%TicketDB{} = ticket, user_id, reason) do
    %TicketDB{id: ticket_id, order_ticket: %OrderTicket{order_id: order_id, bill: %Bill{total: total}}} = ticket
    refund_transaction_payload = create_refund_transaction_payload(total, ticket, user_id, reason)

    with {_, {:ok, _refund_transaction}} <-
           {:refund_transaction, create_refund_transaction(refund_transaction_payload)},
         {_, {:ok, _updated_ticket}} <-
           {:refund, TicketDB.set_status(ticket, :REFUNDED, user_id, nil, nil, true, %{})},
         {_, :ok} <-
           {:refund_order, maybe_set_order_as_refunded(order_id)} do
      Logger.debug("Set direct ticket #{ticket_id} to REFUNDED")
      :ok
    else
      error -> handle_refund_error(error, ticket_id, "direct ticket")
    end
  end

  @spec refund_direct_ticket_group(TicketGroup.t(), Ecto.UUID.t(), atom()) :: :ok | {:error, any()}
  defp refund_direct_ticket_group(%TicketGroup{} = ticket_group, user_id, reason) do
    %TicketGroup{id: ticket_group_id, tickets: tickets, bill: %Bill{total: total}} = ticket_group
    refund_transaction_payload = create_refund_transaction_payload(total, ticket_group, user_id, reason)

    with {_, {:ok, _refund_transaction}} <-
           {:refund_transaction, create_refund_transaction(refund_transaction_payload)},
         {_, :ok} <-
           {:update_tickets, update_tickets_to_refunded(tickets, user_id)},
         {_, :ok} <-
           {:refund_orders, maybe_set_orders_as_refunded(tickets)} do
      Logger.debug("Set direct ticket group #{ticket_group_id} to REFUNDED")
      :ok
    else
      error -> handle_refund_error(error, ticket_group_id, "direct ticket group")
    end
  end

  @spec refund_adyen_paid_ticket(TicketDB.t(), map(), String.t(), map()) :: :ok | {:error, any()}
  defp refund_adyen_paid_ticket(
         %TicketDB{id: ticket_id} = ticket,
         adyen_payload,
         psp_reference,
         %{user_id: user_id} = params
       ) do
    Logger.debug("Refund paid ticket #{ticket_id} with payload #{inspect(adyen_payload)}")

    refund_transaction_payload =
      create_refund_transaction_payload(adyen_payload, {user_id, ticket_id, :TICKET, psp_reference}, params)

    with {_, {:ok, %{id: refund_transaction_id} = refund_transaction}} <-
           {:create_refund_transaction, create_refund_transaction(refund_transaction_payload)},
         updated_payload = Map.put(adyen_payload, :reference, refund_transaction_id),
         {_, {{:ok, response}, _refund_transaction}} <-
           {:process_refund, {Checkout.refunds(updated_payload, psp_reference), refund_transaction}},
         {_, {:ok, _updated_refund_transaction}} <-
           {:update_refund_transaction, update_refund_transaction_with_response(refund_transaction, response)},
         {_, {:ok, _updated_ticket}} <-
           {:update_status, TicketDB.set_status(ticket, :REFUNDING, user_id, nil, nil, false, response)} do
      Logger.debug("Refund paid ticket #{ticket_id} with response #{inspect(response)}")
      :ok
    else
      error -> handle_paid_refund_error(error, ticket_id, "paid via adyen ticket")
    end
  end

  @spec refund_adyen_paid_ticket_group(TicketGroup.t(), map(), String.t(), map()) ::
          :ok | {:error, any()}
  defp refund_adyen_paid_ticket_group(
         %TicketGroup{} = ticket_group,
         adyen_payload,
         psp_reference,
         %{user_id: user_id} = params
       ) do
    %TicketGroup{id: ticket_group_id, tickets: tickets} = ticket_group

    Logger.debug("Refund paid ticket group #{ticket_group_id} with payload #{inspect(adyen_payload)}")

    refund_transaction_payload =
      create_refund_transaction_payload(adyen_payload, {user_id, ticket_group_id, :TICKET_GROUP, psp_reference}, params)

    with {_, {:ok, %{id: refund_transaction_id} = refund_transaction}} <-
           {:create_refund_transaction, create_refund_transaction(refund_transaction_payload)},
         updated_payload = Map.put(adyen_payload, :reference, refund_transaction_id),
         {_, {{:ok, response}, _refund_transaction}} <-
           {:process_refund, {Checkout.refunds(updated_payload, psp_reference), refund_transaction}},
         {_, {:ok, _updated_refund_transaction}} <-
           {:update_refund_transaction, update_refund_transaction_with_response(refund_transaction, response)},
         {_, :ok} <-
           {:update_tickets, update_tickets_to_refunding(tickets, user_id, response)} do
      Logger.debug("Refund paid ticket group #{ticket_group_id} with response #{inspect(response)}")
      :ok
    else
      error -> handle_paid_refund_error(error, ticket_group_id, "paid via adyen ticket group")
    end
  end

  @spec handle_refund_error(any(), Ecto.UUID.t(), String.t()) :: {:error, any()}
  defp handle_refund_error(error, item_id, item_type) do
    case error do
      {:refund_transaction, {operation, failed_value}} ->
        Logger.error(
          "Can't create refund transaction for #{item_type} #{item_id} because of #{inspect(operation)}: #{inspect(failed_value)}"
        )

        {:error, failed_value}

      {:refund, error} ->
        Logger.error("Can't set #{item_type} #{item_id} to REFUNDED because of #{inspect(error)}")
        {:error, error}

      {:update_tickets, error} ->
        Logger.error("Can't update tickets for #{item_type} #{item_id} because of #{inspect(error)}")
        {:error, error}

      {:refund_order, {:error, error}} ->
        Logger.error("Can't set order to REFUNDED for #{item_type} #{item_id} because of #{inspect(error)}")
        {:error, error}

      {:refund_orders, {:error, error}} ->
        Logger.error("Can't set orders to REFUNDED for #{item_type} #{item_id} because of #{inspect(error)}")
        {:error, error}
    end
  end

  @spec handle_paid_refund_error(any(), Ecto.UUID.t(), String.t()) :: {:error, any()}
  defp handle_paid_refund_error(error, item_id, item_type) do
    case error do
      {:create_refund_transaction, {operation, failed_value}} ->
        Logger.error("Can't create refund transaction for #{item_type} #{item_id} because of #{inspect(operation)}")
        {:error, failed_value}

      {:process_refund, {{:error, refund_error}, refund_transaction}} ->
        Logger.error("Can't refund #{item_type} #{item_id} because of refund error #{inspect(refund_error)}")

        case update_refund_transaction_to_failed(refund_transaction, refund_error) do
          {:ok, _updated_transaction} ->
            Logger.debug("Updated refund transaction #{refund_transaction.id} status to FAILED")

          {:error, update_error} ->
            Logger.error(
              "Failed to update refund transaction #{refund_transaction.id} to FAILED status: #{inspect(update_error)}"
            )
        end

        {:error, refund_error}

      {:update_refund_transaction, {:error, error}} ->
        Logger.error("Can't update refund transaction for #{item_type} #{item_id} because of #{inspect(error)}")
        {:error, error}

      {:update_status, error} ->
        Logger.error("Can't refund #{item_type} #{item_id} because of status change error #{inspect(error)}")
        {:error, error}

      {:update_tickets, error} ->
        Logger.error("Can't update tickets for #{item_type} #{item_id} because of #{inspect(error)}")
        {:error, error}

      other ->
        Logger.error("Unexpected error refunding #{item_type} #{item_id}: #{inspect(other)}")
        {:error, other}
    end
  end

  @spec update_tickets_to_refunding([TicketDB.t()], Ecto.UUID.t(), map()) :: :ok | {:error, any()}
  defp update_tickets_to_refunding(tickets, user_id, response) do
    Enum.reduce_while(tickets, :ok, fn ticket, _acc ->
      case TicketDB.set_status(ticket, :REFUNDING, user_id, nil, nil, false, response) do
        {:ok, _updated_ticket} -> {:cont, :ok}
        {:error, error} -> {:halt, {:error, error}}
      end
    end)
  end

  @spec maybe_set_orders_as_refunded([TicketDB.t()]) :: :ok | {:error, any()}
  defp maybe_set_orders_as_refunded(tickets) do
    tickets
    |> Enum.map(& &1.order_ticket.order_id)
    |> Enum.uniq()
    |> Enum.reduce_while(:ok, fn order_id, _acc ->
      case maybe_set_order_as_refunded(order_id) do
        :ok -> {:cont, :ok}
        {:error, error} -> {:halt, {:error, error}}
      end
    end)
  end

  @doc """
   Finalize ticket refund is a function that is called while processing a refund ticket webhook.
   Since Adyen requires a webhook to be processed within 5 seconds and always return a 200 status code, further error handling
   is not possible in this function. If an error occurs, it is logged and the error is returned as a tuple.
  """
  @spec revert_refund(refund_transaction :: RefundTransaction.t(), item :: map()) ::
          atom() | {:error, any()}
  def revert_refund(
        %RefundTransaction{id: refund_transaction_id, refund_item_id: refund_item_id, refund_item_type: :ORDER} =
          refund_transaction,
        item
      ) do
    order = Order.get(refund_item_id)

    Multi.new()
    |> Multi.insert(
      :insert_transaction_history,
      TransactionHistory.changeset(%TransactionHistory{}, %{
        "state" => "FAILED",
        "transaction_id" => refund_transaction_id
      })
    )
    |> Multi.update(:update_order, Order.changeset(order, %{status: :TIMEDOUT}))
    |> Multi.run(:insert_order_history, fn _repo, _changes ->
      OrderHistory.create(order, :TIMEDOUT)
    end)
    |> Multi.update(
      :update_refund_transaction,
      RefundTransaction.changeset(refund_transaction, %{status: "FAILED", psp_result: item})
    )
    |> Repo.transaction()
    |> case do
      {:ok, _transaction_data} -> :ok
      {:error, operation, failed_value, _changes_so_far} -> {:error, {operation, failed_value}}
    end
  end

  def revert_refund(
        %RefundTransaction{refund_item_id: refund_item_id, refund_item_type: :TICKET} = refund_transaction,
        item
      ) do
    with {:ticket,
          %TicketDB{
            id: _ticket_id,
            status: current_status,
            order_ticket: %OrderTicket{order_id: order_id}
          } = ticket} <-
           {:ticket, TicketDB.get(refund_item_id)},
         {:status_change, true} <-
           {:status_change, TicketDB.status_change_allowed?(current_status, :ACTIVE)},
         {:revert_transaction, {:ok, updated_ticket}} <-
           {:revert_transaction, revert_refund_transaction(ticket, refund_transaction, item)} do
      order_id
      |> Order.get()
      |> TicketDB.maybe_revert_order_status()
      |> case do
        :ok ->
          Ticket.update_tickets_counter(updated_ticket)
          :ok

        {:error, error} ->
          Logger.error("Can't revert order status for order #{order_id} because of #{inspect(error)}")

          {:error, error}
      end
    else
      error -> {:error, error}
    end
  end

  @doc """
   Finalize ticket refund is a function that is called while processing a refund ticket webhook.
   Since Adyen requires a webhook to be processed within 5 seconds and always return a 200 status code, further error handling
   is not possible in this function. If an error occurs, it is logged and the error is returned as a tuple.
  """
  @spec finalize_ticket_refund(ticket :: TicketDB.t(), item :: map()) :: atom() | {:error, any()}
  def finalize_ticket_refund(%TicketDB{} = ticket, item) do
    %TicketDB{
      id: ticket_id,
      status: current_status,
      order_ticket: %OrderTicket{order_id: order_id}
    } = ticket

    with {:status_change, true} <-
           {:status_change, TicketDB.status_change_allowed?(current_status, :REFUNDED)},
         {:refund_transaction, %RefundTransaction{} = refund_transaction} <-
           {:refund_transaction, get_refund_transaction_by_type(ticket_id, :TICKET)},
         {:finalize_transaction, {:ok, updated_ticket}} <-
           {:finalize_transaction, finalize_refund_transaction(ticket, refund_transaction, item)} do
      order_id
      |> TicketDB.count_not_refunded_tickets_for_order_by_order_id()
      |> TicketDB.maybe_set_order_as_refunded(order_id)
      |> case do
        :ok ->
          Ticket.update_tickets_counter(updated_ticket)

        {:error, error} ->
          Logger.error("Can't set order as refunded for order #{order_id} because of #{inspect(error)}")

          {:error, error}
      end
    else
      error -> {:error, error}
    end
  end

  @spec list_refund_transactions(params :: map()) :: Scrivener.Page.t()
  def list_refund_transactions(params) do
    query =
      from(rt in RefundTransaction,
        as: :refund_transaction,
        inner_join: t in TicketDB,
        as: :ticket,
        on: rt.refund_item_id == t.id and rt.refund_item_type == :TICKET,
        inner_join: pi in PersonalInformation,
        as: :personal_information,
        on: t.attendee_id == pi.id,
        inner_join: ot in OrderTicket,
        as: :order_ticket,
        on: ot.ticket_id == t.id,
        left_join: o in Order,
        as: :order,
        on: ot.order_id == o.id
      )

    query
    |> where(^filter_where_params(params))
    |> Repo.paginate(params)
  end

  @spec list_refund_transactions_by_ticket_ids(ticket_ids :: [Ecto.UUID.t()]) :: [map()]
  def list_refund_transactions_by_ticket_ids(ticket_ids) do
    RefundTransaction
    |> join(:inner, [rt], t in TicketDB, on: rt.refund_item_id == t.id)
    |> join(:inner, [rt, t], pi in PersonalInformation, on: t.attendee_id == pi.id)
    |> where([rt, t, pi], t.id in ^ticket_ids and rt.refund_item_type == :TICKET)
    |> select([rt, t, pi], %{refund: rt, ticket: t, ticket_personal_information: pi})
    |> Repo.all()
  end

  @spec create_refund_transaction(attrs :: map()) ::
          {:ok, RefundTransaction.t()} | {:error, {atom(), any()}}
  def create_refund_transaction(attrs \\ %{}) do
    Multi.new()
    |> Multi.insert(
      :insert_refund_transaction,
      RefundTransaction.changeset(%RefundTransaction{}, attrs)
    )
    |> Multi.insert(:insert_transaction_history, fn %{
                                                      insert_refund_transaction: refund_transaction
                                                    } ->
      TransactionHistory.changeset(
        %TransactionHistory{},
        %{
          "state" => Atom.to_string(refund_transaction.status),
          "transaction_id" => refund_transaction.id
        }
      )
    end)
    |> Repo.transaction()
    |> case do
      {:ok, %{insert_refund_transaction: refund_transaction}} -> {:ok, refund_transaction}
      {:error, operation, failed_value, _changes_so_far} -> {:error, {operation, failed_value}}
    end
  end

  @spec update_refund_transaction_with_response(
          refund_transaction :: RefundTransaction.t(),
          psp_response :: map()
        ) ::
          {:ok, RefundTransaction.t()} | {:error, Ecto.Changeset.t()}
  def update_refund_transaction_with_response(%RefundTransaction{} = refund_transaction, psp_response) do
    refund_transaction
    |> RefundTransaction.changeset(%{psp_result: psp_response})
    |> Repo.update()
  end

  @spec update_refund_transaction_to_failed(
          refund_transaction :: RefundTransaction.t(),
          error :: any()
        ) ::
          {:ok, RefundTransaction.t()} | {:error, Ecto.Changeset.t()}
  def update_refund_transaction_to_failed(%RefundTransaction{} = refund_transaction, error) do
    refund_transaction
    |> RefundTransaction.changeset(%{
      status: :FAILED,
      psp_result: %{error: inspect(error)}
    })
    |> Repo.update()
  end

  @spec merchant_reference_valid_uuid?(merchant_reference :: String.t()) :: boolean()
  def merchant_reference_valid_uuid?(merchant_reference) do
    case Ecto.UUID.cast(merchant_reference) do
      {:ok, _} -> true
      _ -> false
    end
  end

  @spec get_refund_transaction(refund_transaction_id :: Ecto.UUID.t()) ::
          RefundTransaction.t() | nil
  def get_refund_transaction(refund_transaction_id) do
    RefundTransaction
    |> where([r], r.id == ^refund_transaction_id)
    |> Repo.one()
  end

  @spec get_refund_transaction_by_type(
          refund_item_id :: Ecto.UUID.t(),
          refund_item_type :: atom()
        ) ::
          RefundTransaction.t() | nil
  def get_refund_transaction_by_type(refund_item_id, refund_item_type \\ :TICKET) do
    RefundTransaction
    |> where([r], r.refund_item_id == ^refund_item_id and r.refund_item_type == ^refund_item_type)
    |> Repo.one()
  end

  @spec finalize_refund_transaction(
          ticket :: TicketDB.t(),
          refund_transaction :: RefundTransaction.t(),
          psp_response :: map()
        ) ::
          {:ok, TicketDB.t()} | {:error, {atom(), any()}}
  def finalize_refund_transaction(
        %TicketDB{} = ticket,
        %RefundTransaction{actor_id: actor_id} = refund_transaction,
        psp_response
      ) do
    Multi.new()
    |> Multi.run(:update_ticket, fn _repo, _changes ->
      TicketDB.set_status(ticket, :REFUNDED, actor_id, nil, nil, true, psp_response)
    end)
    |> Multi.insert(
      :insert_transaction_history,
      TransactionHistory.changeset(%TransactionHistory{}, %{
        "state" => "REFUNDED",
        "transaction_id" => refund_transaction.id
      })
    )
    |> Multi.update(
      :update_refund_transaction,
      RefundTransaction.changeset(refund_transaction, %{
        status: "COMPLETED",
        psp_result: psp_response
      })
    )
    |> Repo.transaction()
    |> case do
      {:ok, %{update_ticket: updated_ticket}} -> {:ok, updated_ticket}
      {:error, operation, failed_value, _changes_so_far} -> {:error, {operation, failed_value}}
    end
  end

  def finalize_refund_transaction(%Order{} = order, %RefundTransaction{} = refund_transaction, psp_response) do
    Multi.new()
    |> Multi.update(:update_order, Order.changeset(order, %{status: :REFUNDED}))
    |> Multi.run(:insert_order_history, fn _repo, _changes ->
      OrderHistory.create(order, :REFUNDED)
    end)
    |> Multi.insert(
      :insert_transaction_history,
      TransactionHistory.changeset(%TransactionHistory{}, %{
        "state" => "REFUNDED",
        "transaction_id" => refund_transaction.id
      })
    )
    |> Multi.update(
      :update_refund_transaction,
      RefundTransaction.changeset(refund_transaction, %{
        status: "COMPLETED",
        psp_result: psp_response
      })
    )
    |> Repo.transaction()
    |> case do
      {:ok, %{update_order: update_order}} -> {:ok, update_order}
      {:error, operation, failed_value, _changes_so_far} -> {:error, {operation, failed_value}}
    end
  end

  defp create_refund_transaction_payload(payload, {user_id, item_id, item_type, psp_reference}, params) do
    %{
      status: "INITIATED",
      currency: @currency,
      amount: payload.amount.value,
      refund_item_id: item_id,
      refund_item_type: item_type,
      psp: "Adyen",
      psp_reference: psp_reference,
      payment_method: payload.payment_method,
      actor_id: user_id,
      reason: Map.get(params, :reason, "REFUND TICKET"),
      comment: Map.get(params, :comment, "Initiated via API by user #{user_id}"),
      support_ticket_id: Map.get(params, :support_ticket_id, nil)
    }
  end

  # Handle ticket group refunds for sellers
  defp create_refund_transaction_payload(
         amount,
         %TicketGroup{id: ticket_group_id, tickets: tickets} = _ticket_group,
         user_id,
         reason
       ) do
    case tickets do
      [%TicketDB{order_ticket: %{order: %{seller_id: seller_id}}} | _] when is_binary(seller_id) ->
        create_seller_refund_transaction_payload(amount, ticket_group_id, user_id, reason, :TICKET_GROUP)

      _ ->
        create_invitation_refund_transaction_payload(ticket_group_id, user_id, reason, :TICKET_GROUP)
    end
  end

  # Handle individual ticket refunds for sellers
  defp create_refund_transaction_payload(
         amount,
         %TicketDB{id: ticket_id, order_ticket: %{order: %{seller_id: seller_id}}} = _ticket,
         user_id,
         reason
       )
       when is_binary(seller_id) do
    create_seller_refund_transaction_payload(amount, ticket_id, user_id, reason, :TICKET)
  end

  # Handle individual ticket refunds for invitations
  defp create_refund_transaction_payload(_amount, %TicketDB{id: ticket_id} = _ticket, user_id, reason) do
    create_invitation_refund_transaction_payload(ticket_id, user_id, reason, :TICKET)
  end

  defp create_invitation_refund_transaction_payload(item_id, user_id, reason, refund_item_type) do
    %{
      status: "COMPLETED",
      currency: @currency,
      amount: 0,
      refund_item_id: item_id,
      refund_item_type: refund_item_type,
      psp: "Adyen",
      payment_method: "Adyen",
      psp_reference: "Adyen",
      actor_id: user_id,
      reason: reason,
      comment: "Initiated via API by user #{user_id}"
    }
  end

  defp create_seller_refund_transaction_payload(amount, item_id, user_id, reason, refund_item_type) do
    %{
      status: "COMPLETED",
      currency: @currency,
      amount: amount,
      refund_item_id: item_id,
      refund_item_type: refund_item_type,
      actor_id: user_id,
      reason: reason,
      comment: "Initiated via API by user #{user_id}"
    }
  end

  defp revert_refund_transaction(
         %TicketDB{} = ticket,
         %RefundTransaction{actor_id: actor_id} = refund_transaction,
         psp_response
       ) do
    Multi.new()
    |> Multi.update(
      :update_ticket,
      TicketDB.changeset(ticket, %{status: :ACTIVE, refunded_at: nil})
    )
    |> Multi.run(:insert_ticket_history, fn _repo, _changes ->
      TicketHistory.create(ticket, :ACTIVE, actor_id, nil, nil, psp_response)
    end)
    |> Multi.insert(
      :insert_transaction_history,
      TransactionHistory.changeset(%TransactionHistory{}, %{
        "state" => "FAILED",
        "transaction_id" => refund_transaction.id
      })
    )
    |> Multi.update(
      :update_refund_transaction,
      RefundTransaction.changeset(refund_transaction, %{
        status: "FAILED",
        psp_result: psp_response
      })
    )
    |> Repo.transaction()
    |> case do
      {:ok, %{update_ticket: updated_ticket}} -> {:ok, updated_ticket}
      {:error, operation, failed_value, _changes_so_far} -> {:error, {operation, failed_value}}
    end
  end

  defp filter_where_params(params) do
    Enum.reduce(params, dynamic(true), fn {key, value}, acc ->
      filter_where(acc, key, value)
    end)
  end

  defp filter_where(dynamic, :event_id, value) when not is_nil(value),
    do: dynamic([ticket: t], ^dynamic and t.event_id == ^value)

  defp filter_where(dynamic, :seller_id, value) when not is_nil(value),
    do: dynamic([order: o], ^dynamic and o.seller_id == ^value)

  defp filter_where(dynamic, :order_id, value) when not is_nil(value),
    do: dynamic([order_ticket: ot], ^dynamic and ot.order_id == ^value)

  defp filter_where(dynamic, :ticket_id, value) when not is_nil(value),
    do: dynamic([ticket: t], ^dynamic and t.id == ^value)

  defp filter_where(dynamic, _key, _value), do: dynamic

  @spec process_order_updates([%{order_ticket: %{order_id: Ecto.UUID.t()}}]) ::
          :ok | {:error, :publish_failed}
  defp process_order_updates(tickets) do
    tickets
    |> unique_order_ids()
    |> publish_order_updates()
    |> process_order_update_stream()
  end

  defp unique_order_ids(tickets) do
    tickets
    |> Enum.map(& &1.order_ticket.order_id)
    |> Enum.uniq()
  end

  defp publish_order_updates(order_ids) do
    Task.async_stream(
      order_ids,
      &OrdersPublisher.publish_order_update/1,
      max_concurrency: 5,
      ordered: false
    )
  end

  defp process_order_update_stream(stream) do
    stream
    |> Stream.map(fn
      {:ok, :ok} -> :ok
      {:ok, {:error, _}} -> :error
      {:error, _} -> :error
    end)
    |> Enum.reduce_while(:ok, fn
      :ok, acc -> {:cont, acc}
      :error, _ -> {:halt, {:error, :order_update_publish_failed}}
    end)
  end

  defp maybe_set_order_as_refunded(order_id) do
    order_id
    |> TicketDB.count_not_refunded_tickets_for_order_by_order_id()
    |> TicketDB.maybe_set_order_as_refunded(order_id)
  end

  defp reference_from_item(%TicketDB{id: ticket_id}), do: ticket_id
  defp reference_from_item(%TicketGroup{id: ticket_group_id}), do: ticket_group_id
end
