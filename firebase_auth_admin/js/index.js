const admin = require("firebase-admin");
const fs = require("fs");
const path = require("path");

const serviceAccount = {
  type: "service_account",
  project_id: "stdts-prod",
  private_key_id: "ff5abe7ebd32873df767b3d0c512dca00bad4af5",
  private_key: `***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************`,
  client_email: "<EMAIL>",
  client_id: "101957616682271260970",
  auth_uri: "https://accounts.google.com/o/oauth2/auth",
  token_uri: "https://oauth2.googleapis.com/token",
  auth_provider_x509_cert_url: "https://www.googleapis.com/oauth2/v1/certs",
  client_x509_cert_url:
    "https://www.googleapis.com/robot/v1/metadata/x509/sa-accounts%40stdts-prod.iam.gserviceaccount.com",
  universe_domain: "googleapis.com",
};

const app = admin.initializeApp({
  credential: admin.credential.cert(serviceAccount),
});

console.log("You are in Project " + app.INTERNAL.credential_.projectId);

// Call the function you want to run
// getUserByEmail("email")
// getUserByID("userId")
// getUserByPhoneNumber("+************");
// createUser("email", "phoneNumber", "password")
// updateUser("gaVh0EyZ9oNC6asbcaoW0HIzKlT2", "<EMAIL>");
// deleteUser("oyk9ovq0yHdLwG18dwN43vRm26n1");
// setUserRoles("userId", ["EVENT_ADMIN", "PROMOTER", "ADMIN"])
// addAdminToRoles("userId")
// listAllUserByRole("role")
// listAllUserByEmailPrefix("emailPrefix")
// countUsers()
// deleteMortalUsers(["<EMAIL>", "<EMAIL>", "<EMAIL>"])
// getUsersWithPhoneButNoEmail();
// getUsersWithPhoneButNoEmailToFile();

//updateUsersFromJsonFile("users_202508141355.json");
//getUsersWithSameEmail("users_202508141355.json");
getAllUsersWithAdminRole();
// get Userdata by email
async function getUserByEmail(email) {
  admin
    .auth(app)
    .getUserByEmail(email)
    .then((userRecord) => {
      console.log("User:", userRecord);
      userRecord;
    })
    .catch((error) => {
      console.log("\x1b[31m Error fetching user data: %s \x1b[0m", error);
    });
}

// get Userdata by ID
async function getUserByID(userId) {
  admin
    .auth(app)
    .getUser(userId)
    .then((userRecord) => {
      console.log("User:", userRecord);
      userRecord;
    })
    .catch((error) => {
      console.log("\x1b[31m Error fetching user data: %s \x1b[0m", error);
    });
}

// get Userdata by phone number
async function getUserByPhoneNumber(phoneNumber) {
  admin
    .auth(app)
    .getUserByPhoneNumber(phoneNumber)
    .then((userRecord) => {
      console.log("User:", userRecord);
      userRecord;
    })
    .catch((error) => {
      console.log("\x1b[31m Error fetching user data: %s \x1b[0m", error);
    });
}

// create a new User
async function createUser(email, phoneNumber, password) {
  admin
    .auth(app)
    .createUser({
      email: email,
      emailVerified: false,
      phoneNumber: phoneNumber,
      password: password,
      disabled: false,
    })
    .then((userRecord) => {
      console.log("Successfully created new user:", userRecord);
      userRecord;
    })
    .catch((error) => {
      console.log("\x1b[31m Error creating new user: %s \x1b[0m", error);
    });
}

// update a existing User
async function updateUser(userId, email) {
  admin
    .auth(app)
    .updateUser(userId, {
      email: email,
    })
    .then((userRecord) => {
      console.log("Successfully updated user:", userRecord);
      userRecord;
    })
    .catch((error) => {
      console.log("\x1b[31m Error updating user: %s \x1b[0m", error);
    });
}

// delete a existing User
async function deleteUser(userId) {
  admin
    .auth(app)
    .deleteUser(userId)
    .then(() => {
      console.log("Successfully deleted user ", userId);
    })
    .catch((error) => {
      console.log("\x1b[31m Error deleting user: %s \x1b[0m", error);
    });
}

// Add a user role
async function setUserRoles(userId, roles) {
  admin
    .auth(app)
    .setCustomUserClaims(userId, { roles: roles })
    .then((userRecord) => {
      console.log("Successfully updated user roles:", userRecord);
    })
    .catch((error) => {
      console.log("\x1b[31m Error setting roles: %s \x1b[0m", error);
    });
}

// Add 'ADMIN' to user roles
async function addAdminToRoles(userId) {
  admin
    .auth(app)
    .getUser(userId)
    .then((userRecord) => {
      console.log("Get User:", userRecord);
      let userRoles = userRecord.customClaims?.roles;
      if (Array.isArray(userRoles) && userRoles.includes("ADMIN")) {
        console.log("User is already Admin");
      } else {
        if (Array.isArray(userRoles)) {
          userRoles.push("ADMIN");
        } else {
          userRoles = ["ADMIN"];
        }
        console.log("New user roles:", userRoles);
        setUserRoles(userId, userRoles);
      }
    });
}

async function countUsers(nextPageToken) {
  let userCount = 0;

  try {
    do {
      const listUsersResult = await admin
        .auth(app)
        .listUsers(1000, nextPageToken);
      userCount += listUsersResult.users.length;
      nextPageToken = listUsersResult.pageToken;
    } while (nextPageToken);

    console.log("Total users:", userCount);
    userCount;
  } catch (error) {
    console.error("Error listing users:", error);
  }
}

async function getUsersWithPhoneButNoEmail() {
  const usersWithPhoneNoEmail = [];
  let nextPageToken;

  try {
    do {
      const listUsersResult = await admin
        .auth(app)
        .listUsers(1000, nextPageToken);

      listUsersResult.users.forEach((userRecord) => {
        if (userRecord.phoneNumber && !userRecord.email) {
          usersWithPhoneNoEmail.push({
            uid: userRecord.uid,
            phoneNumber: userRecord.phoneNumber,
            displayName: userRecord.displayName || null,
            creationTime: userRecord.metadata.creationTime,
            lastSignInTime: userRecord.metadata.lastSignInTime,
          });
        }
      });

      nextPageToken = listUsersResult.pageToken;
    } while (nextPageToken);

    console.log(
      `Found ${usersWithPhoneNoEmail.length} users with phone number but no email`
    );
    console.log(usersWithPhoneNoEmail);
    return usersWithPhoneNoEmail;
  } catch (error) {
    console.log("\x1b[31m Error listing users: %s \x1b[0m", error);
    return [];
  }
}

async function getUsersWithPhoneButNoEmailToFile() {
  const usersWithPhoneNoEmail = [];
  let nextPageToken;

  try {
    do {
      const listUsersResult = await admin
        .auth(app)
        .listUsers(1000, nextPageToken);

      listUsersResult.users.forEach((userRecord) => {
        if (userRecord.phoneNumber && !userRecord.email) {
          usersWithPhoneNoEmail.push({
            uid: userRecord.uid,
            phoneNumber: userRecord.phoneNumber,
            displayName: userRecord.displayName || null,
            emailVerified: userRecord.emailVerified,
            disabled: userRecord.disabled,
            creationTime: userRecord.metadata.creationTime,
            lastSignInTime: userRecord.metadata.lastSignInTime,
            customClaims: userRecord.customClaims || null,
          });
        }
      });

      nextPageToken = listUsersResult.pageToken;
    } while (nextPageToken);

    // Create the result object
    const result = {
      timestamp: new Date().toISOString(),
      totalFound: usersWithPhoneNoEmail.length,
      projectId: app.INTERNAL.credential_.projectId,
      users: usersWithPhoneNoEmail,
    };

    // Write to JSON file
    const fileName = `users_phone_no_email_${Date.now()}.json`;
    const filePath = path.join(__dirname, fileName);

    const uids = result.users.map((user) => `'${user.uid}'`);
    const sqlInClause = `(${uids.join(", ")})`;

    console.log(`SQL IN CLAUSE: ${sqlInClause}`);

    fs.writeFileSync(filePath, JSON.stringify(result, null, 2), "utf8");

    console.log(
      `Found ${usersWithPhoneNoEmail.length} users with phone number but no email`
    );
    console.log(`Results written to: ${fileName}`);

    return filePath;
  } catch (error) {
    console.log(
      "\x1b[31m Error listing users or writing file: %s \x1b[0m",
      error
    );
    return null;
  }
}

function listAllUserByEmailPrefix(emailPrefix, nextPageToken) {
  // List batch of users, 1000 at a time (this is the max value).
  admin
    .auth(app)
    .listUsers(1000, nextPageToken)
    .then((listUsersResult) => {
      listUsersResult.users.forEach((userRecord) => {
        if (userRecord.email?.startsWith(emailPrefix)) {
          console.log("--------------");
          console.log("UID: ", userRecord.uid);
          console.log("Email: ", userRecord.email);
          console.log("--------------");
        }
      });
      if (listUsersResult.pageToken) {
        // List next batch of users.
        listAllUserByEmailPrefix(emailPrefix, listUsersResult.pageToken);
      }
    })
    .catch((error) => {
      console.log("\x1b[31m Error listing users: %s \x1b[0m", error);
    });
}

function listAllUserByRole(role, nextPageToken) {
  // List batch of users, 1000 at a time (this is the max value).
  admin
    .auth(app)
    .listUsers(1000, nextPageToken)
    .then((listUsersResult) => {
      listUsersResult.users.forEach((userRecord) => {
        let userRoles = userRecord.customClaims?.roles;
        if (Array.isArray(userRoles) && userRoles.includes(role)) {
          console.log("--------------");
          console.log("UID: ", userRecord.uid);
          console.log("Email: ", userRecord.email);
          console.log("--------------");
        }
      });
      if (listUsersResult.pageToken) {
        // List next batch of users.
        listAllUserByRole(role, listUsersResult.pageToken);
      }
    })
    .catch((error) => {
      console.log("Error listing users:", error);
    });
}

function deleteMortalUsers(immortalUsers, nextPageToken) {
  if (app.INTERNAL.credential_.projectId == "stdts-dev") {
    console.log(
      "Your are in dev, I'll delete all users but not: ",
      immortalUsers
    );

    admin
      .auth(app)
      .listUsers(1000, nextPageToken)
      .then((listUsersResult) => {
        listUsersResult.users.forEach((userRecord) => {
          if (userRecord.email && immortalUsers.includes(userRecord.email)) {
            console.log("--------------");
            console.log("Found immmortal user:");
            console.log("UID: ", userRecord.uid);
            console.log("Email: ", userRecord.email);
            console.log("--------------");
          } else {
            console.log("Delete User ", userRecord.uid);
            deleteUser(userRecord.uid);
          }
        });
        if (listUsersResult.pageToken) {
          deleteMortalUsers(immortalUsers, listUsersResult.pageToken);
        }
      })
      .catch((error) => {
        console.log("\x1b[31m Error deleting users: %s \x1b[0m", error);
      });
  } else {
    console.log("\x1b[31m You are not in dev. I'll do nothing!!! \x1b[31m");
  }
}

async function updateUsersFromJsonFile(filePath) {
  try {
    // Read and parse the JSON file
    const jsonData = JSON.parse(fs.readFileSync(filePath, "utf8"));

    console.log(`Found ${jsonData.length} users to update`);

    let successCount = 0;
    let errorCount = 0;
    const errors = [];

    // Process each user in the array
    for (const user of jsonData) {
      try {
        // Use external_id as Firebase UID and update with email
        await admin.auth(app).updateUser(user.external_id, {
          email: user.email,
          emailVerified: false, // Set to true if you want emails to be verified
        });

        console.log(
          `✅ Successfully updated user ${user.external_id} with email: ${user.email}`
        );
        successCount++;
      } catch (error) {
        console.log(
          `\x1b[31m ❌ Error updating user ${user.external_id}: ${error.message} \x1b[0m`
        );
        errors.push({
          external_id: user.external_id,
          email: user.email,
          error: error.message,
        });
        errorCount++;
      }

      // Add small delay to avoid rate limiting
      await new Promise((resolve) => setTimeout(resolve, 100));
    }

    // Summary
    console.log("\n📊 Update Summary:");
    console.log(`✅ Successfully updated: ${successCount} users`);
    console.log(`❌ Failed to update: ${errorCount} users`);

    if (errors.length > 0) {
      console.log("\n🚨 Errors encountered:");
      errors.forEach((err) => {
        console.log(`- ${err.external_id} (${err.email}): ${err.error}`);
      });
    }

    return {
      success: successCount,
      failed: errorCount,
      errors: errors,
    };
  } catch (error) {
    console.log(
      `\x1b[31m Error reading or parsing JSON file: ${error.message} \x1b[0m`
    );
    return null;
  }
}

async function getUsersWithSameEmail(filePath) {
  try {
    // Read and parse the JSON file
    const jsonData = JSON.parse(fs.readFileSync(filePath, "utf8"));

    console.log(`Found ${jsonData.length} users`);

    const usersWithSameEmail = [];

    // Process each user in the array
    for (const user of jsonData) {
      // Use external_id as Firebase UID and update with email
      await admin
        .auth(app)
        .getUserByEmail(user.email)
        .then((userRecord) => {
          usersWithSameEmail.push(user);
        })
        .catch((error) => {
          console.log(
            `\x1b[31m ❌ Error getting user ${user.external_id}: ${error.message} \x1b[0m`
          );
        });

      // Add small delay to avoid rate limiting
      await new Promise((resolve) => setTimeout(resolve, 100));
    }

    console.log(`Found ${usersWithSameEmail.length} users with same email`);
    console.log(usersWithSameEmail);

    return usersWithSameEmail;
  } catch (error) {
    console.log(
      `\x1b[31m Error reading or parsing JSON file: ${error.message} \x1b[0m`
    );
    return null;
  }
}

async function getAllUsersWithAdminRole() {
  const adminUsers = [];
  let nextPageToken;

  try {
    do {
      const listUsersResult = await admin
        .auth(app)
        .listUsers(1000, nextPageToken);

      listUsersResult.users.forEach((userRecord) => {
        const userRoles = userRecord.customClaims?.roles;
        if (Array.isArray(userRoles) && userRoles.includes("ADMIN")) {
          adminUsers.push({
            uid: userRecord.uid,
            email: userRecord.email || null,
            phoneNumber: userRecord.phoneNumber || null,
            displayName: userRecord.displayName || null,
            emailVerified: userRecord.emailVerified,
            disabled: userRecord.disabled,
            customClaims: userRecord.customClaims,
            creationTime: userRecord.metadata.creationTime,
            lastSignInTime: userRecord.metadata.lastSignInTime,
          });

          console.log("--------------");
          console.log("ADMIN User Found:");
          console.log("UID:", userRecord.uid);
          console.log("Email:", userRecord.email);
          console.log("Roles:", userRoles);
          console.log("--------------");
        }
      });

      nextPageToken = listUsersResult.pageToken;
    } while (nextPageToken);

    console.log(`\n📊 Found ${adminUsers.length} users with ADMIN role`);
    return adminUsers;
  } catch (error) {
    console.log(
      "\x1b[31m Error listing users with ADMIN role: %s \x1b[0m",
      error
    );
    return [];
  }
}
